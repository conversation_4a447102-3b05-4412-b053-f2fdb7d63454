generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider  = "postgresql"
  url       = env("DATABASE_URL")
  directUrl = env("DIRECT_URL")
}

model User {
  id    String  @id @default(cuid())
  email String?  @unique
  phone String? @unique

  name     String?
  birthday String?

  OTP                   String?
  emailUnsubscribeToken String?  @unique @default(cuid())
  referralCode          String? @unique

  isBanned          Boolean @default(false)
  isEmailVerified   Boolean @default(false)
  isPhoneVerified   Boolean @default(false)
  isEmailSubscribed Boolean @default(false)
  isPhoneSubscribed Boolean @default(false)

  cart     Cart?
  wishlist Product[] @relation("Wishlist")

  orders         Order[]
  addresses      Address[]
  payments       Payment[]
  notifications  Notification[]
  productReviews ProductReview[]
  errors         Error[]
  files          File[]

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model Cart {
  user   User       @relation(fields: [userId], references: [id])
  userId String     @id
  items  CartItem[]

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model CartItem {
  cart      Cart    @relation(fields: [cartId], references: [userId])
  cartId    String
  product   Product @relation(fields: [productId], references: [id])
  productId String
  count     Int

  @@unique([cartId, productId], name: "UniqueCartItem")
}

model Owner {
  id     String  @id @default(cuid())
  email  String  @unique
  phone  String? @unique
  name   String?
  avatar String?
  OTP    String?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model Author {
  id     String  @id @default(cuid())
  email  String  @unique
  phone  String? @unique
  name   String?
  avatar String?
  OTP    String?

  blogs Blog[]

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model Brand {
  id          String  @id @default(cuid())
  title       String  @unique
  description String?
  logo        String?

  products Product[]
}

model Product {
  id          String   @id @default(cuid())
  title       String
  description String?
  images      String[]
  keywords    String[]
  metadata    Json?

  price    Float @default(100)
  discount Float @default(0)
  stock    Int   @default(0)

  isPhysical  Boolean @default(true)
  isAvailable Boolean @default(false)
  isFeatured  Boolean @default(false)

  orders         OrderItem[]
  cartItems      CartItem[]
  wishlists      User[]          @relation("Wishlist")
  productReviews ProductReview[]

  brand   Brand  @relation(fields: [brandId], references: [id])
  brandId String

  categories Category[]

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([brandId])
}

model Category {
  id          String  @id @default(cuid())
  title       String  @unique
  description String?

  products Product[]
  banners  Banner[]

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model ProductReview {
  id     String @id @default(cuid())
  text   String
  rating Int

  product   Product @relation(fields: [productId], references: [id])
  productId String
  user      User    @relation(fields: [userId], references: [id])
  userId    String

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@unique([productId, userId], name: "UniqueProductProductReview")
  @@index([userId])
  @@index([productId])
}

model Order {
  id     String          @id @default(cuid())
  number Int             @unique @default(autoincrement())
  status OrderStatusEnum

  total    Float @default(100)
  shipping Float @default(100)
  payable  Float @default(100)
  tax      Float @default(100)
  discount Float @default(0)

  isPaid      Boolean @default(false)
  isCompleted Boolean @default(false)

  payments   Payment[]
  orderItems OrderItem[]
  refund     Refund?

  discountCode   DiscountCode? @relation(fields: [discountCodeId], references: [id])
  discountCodeId String?
  address        Address?      @relation(fields: [addressId], references: [id])
  addressId      String?
  user           User          @relation(fields: [userId], references: [id])
  userId         String

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([userId])
  @@index([addressId])
  @@index([discountCodeId])
}

model OrderItem {
  order     Order   @relation(fields: [orderId], references: [id])
  orderId   String
  product   Product @relation(fields: [productId], references: [id])
  productId String
  count     Int
  price     Float
  discount  Float

  @@unique([orderId, productId], name: "UniqueOrderItem")
}

enum OrderStatusEnum {
  Processing
  Shipped
  Delivered
  ReturnProcessing
  ReturnCompleted
  Cancelled
  RefundProcessing
  RefundCompleted
  Denied
}

model Address {
  id         String @id @default(cuid())
  country    String @default("IRI")
  address    String
  city       String
  phone      String
  postalCode String

  user   User    @relation(fields: [userId], references: [id])
  userId String
  orders Order[]

  createdAt DateTime @default(now())

  @@index([userId])
}

model Notification {
  id      String  @id @default(cuid())
  content String
  isRead  Boolean @default(false)

  user   User   @relation(fields: [userId], references: [id])
  userId String

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([userId])
}

model DiscountCode {
  id                String   @id @default(cuid())
  code              String   @unique
  stock             Int      @default(1)
  description       String?
  percent           Int
  maxDiscountAmount Float    @default(1)
  startDate         DateTime
  endDate           DateTime

  order Order[]

  createdAt DateTime @default(now())
}

model Refund {
  id     String @id @default(cuid())
  amount Float
  reason String

  order   Order  @relation(fields: [orderId], references: [id])
  orderId String @unique

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([orderId])
}

model Payment {
  id     String            @id @default(cuid())
  number Int               @unique @default(autoincrement())
  status PaymentStatusEnum

  refId    String  @unique
  cardPan  String?
  cardHash String?
  fee      Float?

  isSuccessful Boolean @default(false)
  payable      Float

  provider   PaymentProvider @relation(fields: [providerId], references: [id])
  providerId String
  user       User            @relation(fields: [userId], references: [id])
  userId     String
  order      Order           @relation(fields: [orderId], references: [id])
  orderId    String

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([userId])
  @@index([providerId])
  @@index([orderId])
}

enum PaymentStatusEnum {
  Processing
  Paid
  Failed
  Denied
}

model PaymentProvider {
  id          String  @id @default(cuid())
  title       String  @unique
  description String?
  websiteUrl  String?
  isActive    Boolean @default(false)

  orders Payment[]
}

model Error {
  id String @id @default(cuid())

  error String

  user   User?   @relation(fields: [userId], references: [id])
  userId String?

  createdAt DateTime @default(now())

  @@index([userId])
}

model File {
  id String @id @default(cuid())

  url String

  user   User   @relation(fields: [userId], references: [id])
  userId String

  createdAt DateTime @default(now())

  @@index([userId])
}

model Blog {
  slug        String  @id
  title       String
  image       String
  description String
  content     String? @db.Text

  categories String[]
  keywords   String[]

  author   Author @relation(fields: [authorId], references: [id])
  authorId String

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([authorId])
}

model Banner {
  id String @id @default(cuid())

  label String
  image String

  categories Category[]

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}
