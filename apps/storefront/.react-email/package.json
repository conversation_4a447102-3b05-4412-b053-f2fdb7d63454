{"name": "react-email-client", "version": "0.0.14", "description": "The React Email preview application", "license": "MIT", "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "format:check": "prettier --check \"**/*.{ts,tsx,md}\"", "format": "prettier --write \"**/*.{ts,tsx,md}\""}, "engines": {"node": ">=16.0.0"}, "dependencies": {"@radix-ui/colors": "3.0.0", "@radix-ui/react-accessible-icon": "^1.1.0", "@radix-ui/react-alert-dialog": "^1.1.2", "@radix-ui/react-aspect-ratio": "^1.1.0", "@radix-ui/react-avatar": "^1.1.1", "@radix-ui/react-collapsible": "^1.1.1", "@radix-ui/react-context-menu": "^2.2.2", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.2", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-menubar": "^1.1.2", "@radix-ui/react-popover": "^1.1.2", "@radix-ui/react-radio-group": "^1.2.1", "@radix-ui/react-scroll-area": "^1.2.0", "@radix-ui/react-select": "^2.1.2", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.1", "@radix-ui/react-tabs": "^1.1.1", "@radix-ui/react-toast": "^1.2.2", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-toggle-group": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.3", "@react-email/components": "^0.0.25", "@react-email/render": "1.0.1", "@react-email/tailwind": "^0.1.0", "class-variance-authority": "^0.7.0", "classnames": "2.5.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "framer-motion": "^11.11.8", "lucide-react": "^0.452.0", "next": "14.2.15", "prism-react-renderer": "2.4.0", "react": "18.3.1", "react-dom": "18.3.1", "react-email": "^3.0.1", "resend": "^4.0.0"}, "devDependencies": {"@types/classnames": "2.3.1", "@types/node": "22.7.5", "@types/react": "18.3.11", "@types/react-dom": "18.3.1", "@typescript-eslint/parser": "^8.8.1", "autoprefixer": "10.4.20", "eslint": "9.12.0", "eslint-config-next": "14.2.15", "eslint-config-prettier": "9.1.0", "eslint-plugin-simple-import-sort": "12.1.1", "eslint-plugin-unused-imports": "4.1.4", "postcss": "8.4.47", "prettier": "3.3.3", "tailwindcss": "3.4.13", "tailwind-merge": "^2.5.3", "tailwindcss-animate": "^1.0.7", "typescript": "5.6.3"}, "readme": "ERROR: No README data found!", "_id": "react-email-client@0.0.14"}