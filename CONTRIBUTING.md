## Contribution Guidelines

Thank you for considering contributing to this repository! We appreciate your interest and contributions to make this project better. To ensure a smooth collaboration, please review the following guidelines before making any contributions.

### Table of Contents

- [Getting Started](https://github.com/sesto-dev/next-prisma-tailwind-ecommerce/blob/main/CONTRIBUTING.md#getting-started)
- [How to Contribute](https://github.com/sesto-dev/next-prisma-tailwind-ecommerce/blob/main/CONTRIBUTING.md/#how-to-contribute)
- [Code Style](https://github.com/sesto-dev/next-prisma-tailwind-ecommerce/blob/main/CONTRIBUTING.md#code-style)
- [Reporting Issues](https://github.com/sesto-dev/next-prisma-tailwind-ecommerce/blob/main/CONTRIBUTING.md#reporting-issues)
- [Submitting Pull Requests](https://github.com/sesto-dev/next-prisma-tailwind-ecommerce/blob/main/CONTRIBUTING.md#submitting-pull-requests)
- [License](https://github.com/sesto-dev/next-prisma-tailwind-ecommerce/blob/main/CONTRIBUTING.md#license)

### Getting Started

To get started, follow these steps:

1.  Fork the repository.
2.  Clone the forked repository to your local machine.
3.  Set up the development environment.
4.  Make your changes and test them thoroughly.
5.  Submit a pull request (PR) with your changes.

### How to Contribute

Contributions to this repository can be made in the following ways:

- Reporting issues: If you encounter any bugs, issues, or have suggestions for improvements, please open an issue in the repository.
- Pull requests: You can contribute to the project by submitting pull requests for bug fixes, new features, or enhancements. Please ensure your changes adhere to the code style guidelines mentioned below.

### Code Style

To maintain a consistent codebase, we follow a specific code style. Please adhere to the following guidelines when making contributions:

- Use descriptive variable and function names.
- Follow the existing code formatting and indentation patterns.
- Write clear and concise comments to improve code readability.
- Avoid excessive or unnecessary code changes that are unrelated to the purpose of your contribution.

### Reporting Issues

If you encounter any issues or have suggestions, please open an issue on GitHub. When reporting issues, provide as much detail as possible, including steps to reproduce the problem and any relevant error messages or screenshots.

### Submitting Pull Requests

To contribute code to this repository, please follow these steps:

1.  Create a new branch from the `main` branch for your changes.
2.  Make your code changes in the new branch.
3.  Test your changes thoroughly to ensure they work as expected.
4.  Commit your changes with clear and descriptive commit messages.
5.  Push your branch to your forked repository.
6.  Open a pull request (PR) from your branch to the `main` branch of this repository.
7.  Provide a detailed description of your changes in the PR, including any relevant information or context.
8.  Ensure that your PR adheres to the code style guidelines and passes any automated tests or checks.

### License

By contributing to this repository, you agree that your contributions will be licensed under the [LICENSE](https://github.com/sesto-dev/next-prisma-tailwind-ecommerce/blob/main/LICENSE.md) file of this project.
