{"timestamp": "2025-07-31T17:17:24.466Z", "totalWarnings": 133, "categorizedWarnings": {"categorized": {"unused-vars": [{"app": "gloopi-storefront", "file": "./src/app/(store)/(routes)/blog/[slug]/page.tsx", "line": 4, "column": 10, "message": "'format' is defined but never used. Allowed unused vars must match /^_/u.", "rule": "@typescript-eslint/no-unused-vars"}, {"app": "gloopi-admin", "file": "./src/app/(store)/(routes)/blog/[slug]/page.tsx", "line": 77, "column": 16, "message": "'error' is defined but never used. Allowed unused caught errors must match /^_/u.", "rule": "@typescript-eslint/no-unused-vars"}, {"app": "gloopi-admin", "file": "./src/app/(store)/(routes)/blog/[slug]/page.tsx", "line": 96, "column": 16, "message": "'error' is defined but never used. Allowed unused caught errors must match /^_/u.", "rule": "@typescript-eslint/no-unused-vars"}, {"app": "gloopi-storefront", "file": "./src/app/(dashboard)/(routes)/banners/components/table.tsx", "line": 4, "column": 18, "message": "'parseISO' is defined but never used. Allowed unused vars must match /^_/u.", "rule": "@typescript-eslint/no-unused-vars"}, {"app": "gloopi-storefront", "file": "./src/app/(dashboard)/(routes)/banners/components/table.tsx", "line": 34, "column": 19, "message": "'updatedAt' is assigned a value but never used. Allowed unused vars must match /^_/u.", "rule": "@typescript-eslint/no-unused-vars"}, {"app": "gloopi-storefront", "file": "./src/app/(dashboard)/(routes)/banners/components/table.tsx", "line": 52, "column": 35, "message": "'createdAt' is assigned a value but never used. Allowed unused vars must match /^_/u.", "rule": "@typescript-eslint/no-unused-vars"}, {"app": "gloopi-storefront", "file": "./src/app/(dashboard)/(routes)/banners/components/table.tsx", "line": 52, "column": 46, "message": "'updatedAt' is assigned a value but never used. Allowed unused vars must match /^_/u.", "rule": "@typescript-eslint/no-unused-vars"}, {"app": "gloopi-admin", "file": "./src/app/(store)/(routes)/cart/components/grid.tsx", "line": 43, "column": 10, "message": "'params' is assigned a value but never used. Allowed unused vars must match /^_/u.", "rule": "@typescript-eslint/no-unused-vars"}, {"app": "gloopi-admin", "file": "./src/app/(store)/(routes)/cart/components/grid.tsx", "line": 44, "column": 10, "message": "'router' is assigned a value but never used. Allowed unused vars must match /^_/u.", "rule": "@typescript-eslint/no-unused-vars"}, {"app": "gloopi-admin", "file": "./src/app/(dashboard)/(routes)/brands/[brandId]/components/brand-form.tsx", "line": 77, "column": 16, "message": "'error' is defined but never used. Allowed unused caught errors must match /^_/u.", "rule": "@typescript-eslint/no-unused-vars"}, {"app": "gloopi-admin", "file": "./src/app/(dashboard)/(routes)/brands/[brandId]/components/brand-form.tsx", "line": 96, "column": 16, "message": "'error' is defined but never used. Allowed unused caught errors must match /^_/u.", "rule": "@typescript-eslint/no-unused-vars"}, {"app": "gloopi-admin", "file": "./src/app/(dashboard)/(routes)/brands/components/table.tsx", "line": 42, "column": 10, "message": "'params' is assigned a value but never used. Allowed unused vars must match /^_/u.", "rule": "@typescript-eslint/no-unused-vars"}, {"app": "gloopi-admin", "file": "./src/app/(dashboard)/(routes)/brands/components/table.tsx", "line": 43, "column": 10, "message": "'router' is assigned a value but never used. Allowed unused vars must match /^_/u.", "rule": "@typescript-eslint/no-unused-vars"}, {"app": "gloopi-admin", "file": "./src/app/(dashboard)/(routes)/categories/[categoryId]/components/category-form.tsx", "line": 86, "column": 16, "message": "'error' is defined but never used. Allowed unused caught errors must match /^_/u.", "rule": "@typescript-eslint/no-unused-vars"}, {"app": "gloopi-admin", "file": "./src/app/(dashboard)/(routes)/categories/[categoryId]/components/category-form.tsx", "line": 105, "column": 16, "message": "'error' is defined but never used. Allowed unused caught errors must match /^_/u.", "rule": "@typescript-eslint/no-unused-vars"}, {"app": "gloopi-admin", "file": "./src/app/(dashboard)/(routes)/categories/components/table.tsx", "line": 42, "column": 10, "message": "'params' is assigned a value but never used. Allowed unused vars must match /^_/u.", "rule": "@typescript-eslint/no-unused-vars"}, {"app": "gloopi-admin", "file": "./src/app/(dashboard)/(routes)/categories/components/table.tsx", "line": 43, "column": 10, "message": "'router' is assigned a value but never used. Allowed unused vars must match /^_/u.", "rule": "@typescript-eslint/no-unused-vars"}, {"app": "gloopi-admin", "file": "./src/app/(dashboard)/(routes)/orders/[orderId]/components/order-form.tsx", "line": 86, "column": 16, "message": "'error' is defined but never used. Allowed unused caught errors must match /^_/u.", "rule": "@typescript-eslint/no-unused-vars"}, {"app": "gloopi-admin", "file": "./src/app/(dashboard)/(routes)/orders/[orderId]/page.tsx", "line": 11, "column": 4, "message": "'CardHeader' is defined but never used. Allowed unused vars must match /^_/u.", "rule": "@typescript-eslint/no-unused-vars"}, {"app": "gloopi-admin", "file": "./src/app/(dashboard)/(routes)/orders/[orderId]/page.tsx", "line": 12, "column": 4, "message": "'CardTitle' is defined but never used. Allowed unused vars must match /^_/u.", "rule": "@typescript-eslint/no-unused-vars"}, {"app": "gloopi-admin", "file": "./src/app/(dashboard)/(routes)/payments/[paymentId]/components/payment-form.tsx", "line": 86, "column": 16, "message": "'error' is defined but never used. Allowed unused caught errors must match /^_/u.", "rule": "@typescript-eslint/no-unused-vars"}, {"app": "gloopi-admin", "file": "./src/app/(dashboard)/(routes)/payments/[paymentId]/page.tsx", "line": 3, "column": 8, "message": "'prisma' is defined but never used. Allowed unused vars must match /^_/u.", "rule": "@typescript-eslint/no-unused-vars"}, {"app": "gloopi-admin", "file": "./src/app/(dashboard)/(routes)/products/[productId]/components/product-form.tsx", "line": 113, "column": 16, "message": "'error' is defined but never used. Allowed unused caught errors must match /^_/u.", "rule": "@typescript-eslint/no-unused-vars"}, {"app": "gloopi-admin", "file": "./src/app/(dashboard)/(routes)/products/[productId]/components/product-form.tsx", "line": 132, "column": 16, "message": "'error' is defined but never used. Allowed unused caught errors must match /^_/u.", "rule": "@typescript-eslint/no-unused-vars"}, {"app": "gloopi-admin", "file": "./src/app/(dashboard)/(routes)/products/components/table.tsx", "line": 15, "column": 10, "message": "'router' is assigned a value but never used. Allowed unused vars must match /^_/u.", "rule": "@typescript-eslint/no-unused-vars"}, {"app": "gloopi-admin", "file": "./src/app/(dashboard)/(routes)/users/[userId]/components/user-form.tsx", "line": 82, "column": 16, "message": "'error' is defined but never used. Allowed unused caught errors must match /^_/u.", "rule": "@typescript-eslint/no-unused-vars"}, {"app": "gloopi-storefront", "file": "./src/app/(dashboard)/(routes)/users/[userId]/components/user-form.tsx", "line": 12, "column": 12, "message": "'loading' is assigned a value but never used. Allowed unused vars must match /^_/u.", "rule": "@typescript-eslint/no-unused-vars"}, {"app": "gloopi-storefront", "file": "./src/app/(dashboard)/(routes)/users/[userId]/components/user-form.tsx", "line": 12, "column": 27, "message": "'refreshCart' is assigned a value but never used. Allowed unused vars must match /^_/u.", "rule": "@typescript-eslint/no-unused-vars"}, {"app": "gloopi-storefront", "file": "./src/app/(dashboard)/(routes)/users/[userId]/components/user-form.tsx", "line": 12, "column": 40, "message": "'dispatchCart' is assigned a value but never used. Allowed unused vars must match /^_/u.", "rule": "@typescript-eslint/no-unused-vars"}, {"app": "gloopi-storefront", "file": "./src/app/(store)/(routes)/cart/components/item.tsx", "line": 9, "column": 4, "message": "'CardDescription' is defined but never used. Allowed unused vars must match /^_/u.", "rule": "@typescript-eslint/no-unused-vars"}, {"app": "gloopi-admin", "file": "./src/app/(dashboard)/(routes)/users/[userId]/page.tsx", "line": 7, "column": 29, "message": "'CardHeader' is defined but never used. Allowed unused vars must match /^_/u.", "rule": "@typescript-eslint/no-unused-vars"}, {"app": "gloopi-admin", "file": "./src/app/(dashboard)/(routes)/users/[userId]/page.tsx", "line": 7, "column": 41, "message": "'CardTitle' is defined but never used. Allowed unused vars must match /^_/u.", "rule": "@typescript-eslint/no-unused-vars"}, {"app": "gloopi-admin", "file": "./src/app/(dashboard)/(routes)/users/components/table.tsx", "line": 66, "column": 10, "message": "'router' is assigned a value but never used. Allowed unused vars must match /^_/u.", "rule": "@typescript-eslint/no-unused-vars"}, {"app": "gloopi-storefront", "file": "./src/app/api/brands/route.ts", "line": 10, "column": 4, "message": "'CardFooter' is defined but never used. Allowed unused vars must match /^_/u.", "rule": "@typescript-eslint/no-unused-vars"}, {"app": "gloopi-storefront", "file": "./src/app/api/brands/route.ts", "line": 12, "column": 4, "message": "'CardTitle' is defined but never used. Allowed unused vars must match /^_/u.", "rule": "@typescript-eslint/no-unused-vars"}, {"app": "gloopi-storefront", "file": "./src/app/api/brands/route.ts", "line": 15, "column": 40, "message": "'writeLocalCart' is defined but never used. Allowed unused vars must match /^_/u.", "rule": "@typescript-eslint/no-unused-vars"}, {"app": "gloopi-storefront", "file": "./src/app/api/brands/route.ts", "line": 20, "column": 10, "message": "'useEffect' is defined but never used. Allowed unused vars must match /^_/u.", "rule": "@typescript-eslint/no-unused-vars"}, {"app": "gloopi-storefront", "file": "./src/app/api/brands/route.ts", "line": 24, "column": 12, "message": "'loading' is assigned a value but never used. Allowed unused vars must match /^_/u.", "rule": "@typescript-eslint/no-unused-vars"}, {"app": "gloopi-storefront", "file": "./src/app/api/brands/route.ts", "line": 24, "column": 27, "message": "'refreshCart' is assigned a value but never used. Allowed unused vars must match /^_/u.", "rule": "@typescript-eslint/no-unused-vars"}, {"app": "gloopi-storefront", "file": "./src/app/api/brands/route.ts", "line": 27, "column": 32, "message": "'count' is assigned a value but never used. Allowed unused vars must match /^_/u.", "rule": "@typescript-eslint/no-unused-vars"}, {"app": "gloopi-admin", "file": "./src/app/(store)/(routes)/cart/components/receipt.tsx", "line": 35, "column": 27, "message": "'req' is defined but never used. Allowed unused args must match /^_/u.", "rule": "@typescript-eslint/no-unused-vars"}, {"app": "gloopi-admin", "file": "./src/app/api/categories/route.ts", "line": 44, "column": 27, "message": "'req' is defined but never used. Allowed unused args must match /^_/u.", "rule": "@typescript-eslint/no-unused-vars"}, {"app": "gloopi-storefront", "file": "./src/app/api/categories/route.ts", "line": 13, "column": 27, "message": "'refreshCart' is assigned a value but never used. Allowed unused vars must match /^_/u.", "rule": "@typescript-eslint/no-unused-vars"}, {"app": "gloopi-storefront", "file": "./src/app/api/categories/route.ts", "line": 13, "column": 40, "message": "'dispatchCart' is assigned a value but never used. Allowed unused vars must match /^_/u.", "rule": "@typescript-eslint/no-unused-vars"}, {"app": "gloopi-admin", "file": "./src/app/api/products/route.ts", "line": 12, "column": 15, "message": "'data' is assigned a value but never used. Allowed unused vars must match /^_/u.", "rule": "@typescript-eslint/no-unused-vars"}, {"app": "gloopi-admin", "file": "./src/app/api/products/route.ts", "line": 32, "column": 13, "message": "'categoryId' is assigned a value but never used. Allowed unused vars must match /^_/u.", "rule": "@typescript-eslint/no-unused-vars"}, {"app": "gloopi-storefront", "file": "./src/app/api/products/route.ts", "line": 2, "column": 4, "message": "'BlogPostCard' is defined but never used. Allowed unused vars must match /^_/u.", "rule": "@typescript-eslint/no-unused-vars"}, {"app": "gloopi-admin", "file": "./src/app/(store)/(routes)/products/[productId]/components/cart_button.tsx", "line": 33, "column": 13, "message": "'isFeatured' is assigned a value but never used. Allowed unused vars must match /^_/u.", "rule": "@typescript-eslint/no-unused-vars"}, {"app": "gloopi-storefront", "file": "./src/app/login/components/user-auth-form.tsx", "line": 21, "column": 12, "message": "'loading' is assigned a value but never used. Allowed unused vars must match /^_/u.", "rule": "@typescript-eslint/no-unused-vars"}, {"app": "gloopi-storefront", "file": "./src/app/login/components/user-auth-form.tsx", "line": 21, "column": 27, "message": "'refreshCart' is assigned a value but never used. Allowed unused vars must match /^_/u.", "rule": "@typescript-eslint/no-unused-vars"}, {"app": "gloopi-storefront", "file": "./src/app/(store)/(routes)/products/[productId]/page.tsx", "line": 16, "column": 14, "message": "'searchParams' is defined but never used. Allowed unused args must match /^_/u.", "rule": "@typescript-eslint/no-unused-vars"}, {"app": "gloopi-storefront", "file": "./src/app/(store)/(routes)/products/[productId]/page.tsx", "line": 17, "column": 4, "message": "'parent' is defined but never used. Allowed unused args must match /^_/u.", "rule": "@typescript-eslint/no-unused-vars"}, {"app": "gloopi-storefront", "file": "./src/app/(store)/(routes)/profile/addresses/[addressId]/components/address-form.tsx", "line": 74, "column": 16, "message": "'error' is defined but never used. Allowed unused caught errors must match /^_/u.", "rule": "@typescript-eslint/no-unused-vars"}, {"app": "gloopi-storefront", "file": "./src/app/(store)/(routes)/profile/addresses/[addressId]/components/address-form.tsx", "line": 93, "column": 16, "message": "'error' is defined but never used. Allowed unused caught errors must match /^_/u.", "rule": "@typescript-eslint/no-unused-vars"}, {"app": "gloopi-storefront", "file": "./src/app/(store)/(routes)/profile/components/switcher.tsx", "line": 8, "column": 4, "message": "'DropdownMenuLabel' is defined but never used. Allowed unused vars must match /^_/u.", "rule": "@typescript-eslint/no-unused-vars"}, {"app": "gloopi-admin", "file": "./src/components/main-nav.tsx", "line": 5, "column": 10, "message": "'useParams' is defined but never used. Allowed unused vars must match /^_/u.", "rule": "@typescript-eslint/no-unused-vars"}, {"app": "gloopi-admin", "file": "./src/components/ui/data-table.tsx", "line": 23, "column": 10, "message": "'Input' is defined but never used. Allowed unused vars must match /^_/u.", "rule": "@typescript-eslint/no-unused-vars"}, {"app": "gloopi-admin", "file": "./src/components/ui/data-table.tsx", "line": 34, "column": 4, "message": "'searchKey' is defined but never used. Allowed unused args must match /^_/u.", "rule": "@typescript-eslint/no-unused-vars"}, {"app": "gloopi-admin", "file": "./src/components/ui/modal.tsx", "line": 7, "column": 4, "message": "'DialogFooter' is defined but never used. Allowed unused vars must match /^_/u.", "rule": "@typescript-eslint/no-unused-vars"}, {"app": "gloopi-admin", "file": "./src/components/ui/modal.tsx", "line": 11, "column": 10, "message": "'Button' is defined but never used. Allowed unused vars must match /^_/u.", "rule": "@typescript-eslint/no-unused-vars"}, {"app": "gloopi-storefront", "file": "./src/components/ui/modal.tsx", "line": 9, "column": 4, "message": "'DropdownMenuSeparator' is defined but never used. Allowed unused vars must match /^_/u.", "rule": "@typescript-eslint/no-unused-vars"}, {"app": "gloopi-storefront", "file": "./src/components/ui/modal.tsx", "line": 17, "column": 11, "message": "'open' is assigned a value but never used. Allowed unused elements of array destructuring must match /^_/u.", "rule": "@typescript-eslint/no-unused-vars"}, {"app": "gloopi-storefront", "file": "./src/components/ui/modal.tsx", "line": 17, "column": 17, "message": "'setOpen' is assigned a value but never used. Allowed unused elements of array destructuring must match /^_/u.", "rule": "@typescript-eslint/no-unused-vars"}, {"app": "gloopi-storefront", "file": "./src/app/(store)/(routes)/profile/edit/components/user-form.tsx", "line": 7, "column": 4, "message": "'FormDescription' is defined but never used. Allowed unused vars must match /^_/u.", "rule": "@typescript-eslint/no-unused-vars"}, {"app": "gloopi-storefront", "file": "./src/app/(store)/(routes)/profile/edit/components/user-form.tsx", "line": 79, "column": 16, "message": "'error' is defined but never used. Allowed unused caught errors must match /^_/u.", "rule": "@typescript-eslint/no-unused-vars"}, {"app": "gloopi-storefront", "file": "./src/app/(store)/(routes)/profile/edit/page.tsx", "line": 3, "column": 29, "message": "'CardHeader' is defined but never used. Allowed unused vars must match /^_/u.", "rule": "@typescript-eslint/no-unused-vars"}, {"app": "gloopi-storefront", "file": "./src/app/(store)/(routes)/profile/edit/page.tsx", "line": 3, "column": 41, "message": "'CardTitle' is defined but never used. Allowed unused vars must match /^_/u.", "rule": "@typescript-eslint/no-unused-vars"}, {"app": "gloopi-storefront", "file": "./src/app/(store)/(routes)/profile/edit/page.tsx", "line": 6, "column": 10, "message": "'format' is defined but never used. Allowed unused vars must match /^_/u.", "rule": "@typescript-eslint/no-unused-vars"}, {"app": "gloopi-admin", "file": "./src/emails/order_notification_owner.tsx", "line": 4, "column": 4, "message": "'Column' is defined but never used. Allowed unused vars must match /^_/u.", "rule": "@typescript-eslint/no-unused-vars"}, {"app": "gloopi-admin", "file": "./src/emails/order_notification_owner.tsx", "line": 13, "column": 4, "message": "'Row' is defined but never used. Allowed unused vars must match /^_/u.", "rule": "@typescript-eslint/no-unused-vars"}, {"app": "gloopi-admin", "file": "./src/emails/verify.tsx", "line": 3, "column": 4, "message": "'Button' is defined but never used. Allowed unused vars must match /^_/u.", "rule": "@typescript-eslint/no-unused-vars"}, {"app": "gloopi-admin", "file": "./src/emails/verify.tsx", "line": 4, "column": 4, "message": "'Column' is defined but never used. Allowed unused vars must match /^_/u.", "rule": "@typescript-eslint/no-unused-vars"}, {"app": "gloopi-admin", "file": "./src/emails/verify.tsx", "line": 10, "column": 4, "message": "'Img' is defined but never used. Allowed unused vars must match /^_/u.", "rule": "@typescript-eslint/no-unused-vars"}, {"app": "gloopi-admin", "file": "./src/emails/verify.tsx", "line": 11, "column": 4, "message": "'Link' is defined but never used. Allowed unused vars must match /^_/u.", "rule": "@typescript-eslint/no-unused-vars"}, {"app": "gloopi-admin", "file": "./src/emails/verify.tsx", "line": 13, "column": 4, "message": "'Row' is defined but never used. Allowed unused vars must match /^_/u.", "rule": "@typescript-eslint/no-unused-vars"}, {"app": "gloopi-storefront", "file": "./src/emails/verify.tsx", "line": 86, "column": 16, "message": "'error' is defined but never used. Allowed unused caught errors must match /^_/u.", "rule": "@typescript-eslint/no-unused-vars"}, {"app": "gloopi-admin", "file": "./src/app/(store)/(routes)/profile/orders/[orderId]/page.tsx", "line": 14, "column": 4, "message": "'Section' is defined but never used. Allowed unused vars must match /^_/u.", "rule": "@typescript-eslint/no-unused-vars"}, {"app": "gloopi-admin", "file": "./src/middleware.ts", "line": 42, "column": 13, "message": "'error' is defined but never used. Allowed unused caught errors must match /^_/u.", "rule": "@typescript-eslint/no-unused-vars"}, {"app": "gloopi-storefront", "file": "./src/middleware.ts", "line": 6, "column": 4, "message": "'CardFooter' is defined but never used. Allowed unused vars must match /^_/u.", "rule": "@typescript-eslint/no-unused-vars"}, {"app": "gloopi-storefront", "file": "./src/middleware.ts", "line": 7, "column": 4, "message": "'CardHeader' is defined but never used. Allowed unused vars must match /^_/u.", "rule": "@typescript-eslint/no-unused-vars"}, {"app": "gloopi-storefront", "file": "./src/middleware.ts", "line": 8, "column": 4, "message": "'CardTitle' is defined but never used. Allowed unused vars must match /^_/u.", "rule": "@typescript-eslint/no-unused-vars"}, {"app": "gloopi-storefront", "file": "./src/app/(store)/(routes)/wishlist/page.tsx", "line": 5, "column": 27, "message": "'validateBoolean' is defined but never used. Allowed unused vars must match /^_/u.", "rule": "@typescript-eslint/no-unused-vars"}, {"app": "gloopi-storefront", "file": "./src/app/(store)/(routes)/wishlist/page.tsx", "line": 14, "column": 11, "message": "'items' is assigned a value but never used. Allowed unused elements of array destructuring must match /^_/u.", "rule": "@typescript-eslint/no-unused-vars"}, {"app": "gloopi-storefront", "file": "./src/app/api/orders/route.ts", "line": 104, "column": 13, "message": "'notifications' is assigned a value but never used. Allowed unused vars must match /^_/u.", "rule": "@typescript-eslint/no-unused-vars"}, {"app": "gloopi-storefront", "file": "./src/app/api/products/route.ts", "line": 4, "column": 27, "message": "'req' is defined but never used. Allowed unused args must match /^_/u.", "rule": "@typescript-eslint/no-unused-vars"}, {"app": "gloopi-storefront", "file": "./src/app/api/subscription/email/route.ts", "line": 26, "column": 13, "message": "'message' is assigned a value but never used. Allowed unused vars must match /^_/u.", "rule": "@typescript-eslint/no-unused-vars"}, {"app": "gloopi-storefront", "file": "./src/app/api/subscription/email/route.ts", "line": 49, "column": 13, "message": "'message' is assigned a value but never used. Allowed unused vars must match /^_/u.", "rule": "@typescript-eslint/no-unused-vars"}, {"app": "gloopi-storefront", "file": "./src/app/api/subscription/phone/route.ts", "line": 26, "column": 13, "message": "'message' is assigned a value but never used. Allowed unused vars must match /^_/u.", "rule": "@typescript-eslint/no-unused-vars"}, {"app": "gloopi-storefront", "file": "./src/app/api/subscription/phone/route.ts", "line": 53, "column": 13, "message": "'message' is assigned a value but never used. Allowed unused vars must match /^_/u.", "rule": "@typescript-eslint/no-unused-vars"}, {"app": "gloopi-storefront", "file": "./src/components/native/BlogCard.tsx", "line": 5, "column": 4, "message": "'CardDescription' is defined but never used. Allowed unused vars must match /^_/u.", "rule": "@typescript-eslint/no-unused-vars"}, {"app": "gloopi-storefront", "file": "./src/components/native/BlogCard.tsx", "line": 8, "column": 4, "message": "'CardTitle' is defined but never used. Allowed unused vars must match /^_/u.", "rule": "@typescript-eslint/no-unused-vars"}, {"app": "gloopi-storefront", "file": "./src/components/native/BlogCard.tsx", "line": 34, "column": 19, "message": "'description' is assigned a value but never used. Allowed unused vars must match /^_/u.", "rule": "@typescript-eslint/no-unused-vars"}, {"app": "gloopi-storefront", "file": "./src/components/native/BlogCard.tsx", "line": 34, "column": 45, "message": "'createdAt' is assigned a value but never used. Allowed unused vars must match /^_/u.", "rule": "@typescript-eslint/no-unused-vars"}, {"app": "gloopi-storefront", "file": "./src/components/native/Product.tsx", "line": 6, "column": 4, "message": "'CardDescription' is defined but never used. Allowed unused vars must match /^_/u.", "rule": "@typescript-eslint/no-unused-vars"}, {"app": "gloopi-storefront", "file": "./src/components/native/Product.tsx", "line": 9, "column": 4, "message": "'CardTitle' is defined but never used. Allowed unused vars must match /^_/u.", "rule": "@typescript-eslint/no-unused-vars"}, {"app": "gloopi-storefront", "file": "./src/components/native/mdx/MDXComponents.tsx", "line": 5, "column": 8, "message": "'Link' is defined but never used. Allowed unused vars must match /^_/u.", "rule": "@typescript-eslint/no-unused-vars"}, {"app": "gloopi-storefront", "file": "./src/components/native/mdx/MDXComponents.tsx", "line": 30, "column": 19, "message": "'title' is defined but never used. Allowed unused args must match /^_/u.", "rule": "@typescript-eslint/no-unused-vars"}, {"app": "gloopi-storefront", "file": "./src/components/native/modal.tsx", "line": 7, "column": 4, "message": "'DialogFooter' is defined but never used. Allowed unused vars must match /^_/u.", "rule": "@typescript-eslint/no-unused-vars"}, {"app": "gloopi-storefront", "file": "./src/components/native/modal.tsx", "line": 11, "column": 10, "message": "'Button' is defined but never used. Allowed unused vars must match /^_/u.", "rule": "@typescript-eslint/no-unused-vars"}, {"app": "gloopi-storefront", "file": "./src/components/native/nav/user.tsx", "line": 9, "column": 4, "message": "'DropdownMenuLabel' is defined but never used. Allowed unused vars must match /^_/u.", "rule": "@typescript-eslint/no-unused-vars"}, {"app": "gloopi-storefront", "file": "./src/components/native/nav/user.tsx", "line": 11, "column": 4, "message": "'DropdownMenuShortcut' is defined but never used. Allowed unused vars must match /^_/u.", "rule": "@typescript-eslint/no-unused-vars"}, {"app": "gloopi-storefront", "file": "./src/components/ui/use-toast.ts", "line": 16, "column": 7, "message": "'actionTypes' is assigned a value but only used as a type. Allowed unused vars must match /^_/u.", "rule": "@typescript-eslint/no-unused-vars"}, {"app": "gloopi-storefront", "file": "./src/emails/order_notification_owner.tsx", "line": 4, "column": 4, "message": "'Column' is defined but never used. Allowed unused vars must match /^_/u.", "rule": "@typescript-eslint/no-unused-vars"}, {"app": "gloopi-storefront", "file": "./src/emails/order_notification_owner.tsx", "line": 13, "column": 4, "message": "'Row' is defined but never used. Allowed unused vars must match /^_/u.", "rule": "@typescript-eslint/no-unused-vars"}, {"app": "gloopi-storefront", "file": "./src/emails/verify.tsx", "line": 3, "column": 4, "message": "'Button' is defined but never used. Allowed unused vars must match /^_/u.", "rule": "@typescript-eslint/no-unused-vars"}, {"app": "gloopi-storefront", "file": "./src/emails/verify.tsx", "line": 4, "column": 4, "message": "'Column' is defined but never used. Allowed unused vars must match /^_/u.", "rule": "@typescript-eslint/no-unused-vars"}, {"app": "gloopi-storefront", "file": "./src/emails/verify.tsx", "line": 10, "column": 4, "message": "'Img' is defined but never used. Allowed unused vars must match /^_/u.", "rule": "@typescript-eslint/no-unused-vars"}, {"app": "gloopi-storefront", "file": "./src/emails/verify.tsx", "line": 11, "column": 4, "message": "'Link' is defined but never used. Allowed unused vars must match /^_/u.", "rule": "@typescript-eslint/no-unused-vars"}, {"app": "gloopi-storefront", "file": "./src/emails/verify.tsx", "line": 13, "column": 4, "message": "'Row' is defined but never used. Allowed unused vars must match /^_/u.", "rule": "@typescript-eslint/no-unused-vars"}, {"app": "gloopi-storefront", "file": "./src/emails/verify.tsx", "line": 14, "column": 4, "message": "'Section' is defined but never used. Allowed unused vars must match /^_/u.", "rule": "@typescript-eslint/no-unused-vars"}, {"app": "gloopi-storefront", "file": "./src/lib/cart.ts", "line": 9, "column": 16, "message": "'error' is defined but never used. Allowed unused caught errors must match /^_/u.", "rule": "@typescript-eslint/no-unused-vars"}, {"app": "gloopi-storefront", "file": "./src/lib/omit.ts", "line": 5, "column": 13, "message": "'key' is assigned a value but never used. Allowed unused vars must match /^_/u.", "rule": "@typescript-eslint/no-unused-vars"}, {"app": "gloopi-storefront", "file": "./src/middleware.ts", "line": 37, "column": 13, "message": "'error' is defined but never used. Allowed unused caught errors must match /^_/u.", "rule": "@typescript-eslint/no-unused-vars"}, {"app": "gloopi-storefront", "file": "./src/state/Cart.tsx", "line": 10, "column": 19, "message": "'object' is defined but never used. Allowed unused args must match /^_/u.", "rule": "@typescript-eslint/no-unused-vars"}, {"app": "gloopi-storefront", "file": "./src/state/Cart.tsx", "line": 18, "column": 12, "message": "'refreshUser' is assigned a value but never used. Allowed unused vars must match /^_/u.", "rule": "@typescript-eslint/no-unused-vars"}, {"app": "gloopi-storefront", "file": "./src/state/User.tsx", "line": 2, "column": 27, "message": "'validateBoolean' is defined but never used. Allowed unused vars must match /^_/u.", "rule": "@typescript-eslint/no-unused-vars"}], "empty-interfaces": [{"app": "gloopi-admin", "file": "./src/app/(store)/(routes)/profile/components/switcher.tsx", "line": 12, "column": 11, "message": "An interface declaring no members is equivalent to its supertype.", "rule": "@typescript-eslint/no-empty-object-type"}, {"app": "gloopi-admin", "file": "./src/components/ui/command.tsx", "line": 26, "column": 11, "message": "An interface declaring no members is equivalent to its supertype.", "rule": "@typescript-eslint/no-empty-object-type"}, {"app": "gloopi-admin", "file": "./src/components/ui/input.tsx", "line": 5, "column": 18, "message": "An interface declaring no members is equivalent to its supertype.", "rule": "@typescript-eslint/no-empty-object-type"}, {"app": "gloopi-storefront", "file": "./src/app/login/components/user-auth-form.tsx", "line": 12, "column": 11, "message": "An interface declaring no members is equivalent to its supertype.", "rule": "@typescript-eslint/no-empty-object-type"}, {"app": "gloopi-storefront", "file": "./src/components/ui/command.tsx", "line": 26, "column": 11, "message": "An interface declaring no members is equivalent to its supertype.", "rule": "@typescript-eslint/no-empty-object-type"}, {"app": "gloopi-storefront", "file": "./src/components/ui/input.tsx", "line": 5, "column": 18, "message": "An interface declaring no members is equivalent to its supertype.", "rule": "@typescript-eslint/no-empty-object-type"}, {"app": "gloopi-storefront", "file": "./src/components/ui/textarea.tsx", "line": 5, "column": 18, "message": "An interface declaring no members is equivalent to its supertype.", "rule": "@typescript-eslint/no-empty-object-type"}], "code-quality": [{"app": "gloopi-storefront", "file": "./src/app/api/categories/route.ts", "line": 20, "column": 29, "message": "Unsafe usage of optional chaining. If it short-circuits with 'undefined' the evaluation will throw TypeError.", "rule": "no-unsafe-optional-chaining"}, {"app": "gloopi-storefront", "file": "./src/app/api/orders/route.ts", "line": 137, "column": 23, "message": "Unsafe usage of optional chaining. If it short-circuits with 'undefined' the evaluation will throw TypeError.", "rule": "no-unsafe-optional-chaining"}, {"app": "gloopi-storefront", "file": "./src/lib/omit.ts", "line": 5, "column": 13, "message": "'key' is never reassigned. Use 'const' instead.", "rule": "prefer-const"}], "typescript-issues": [{"app": "gloopi-storefront", "file": "./src/components/native/CompareImage.tsx", "line": 98, "column": 9, "message": "Expected an assignment or function call and instead saw an expression.", "rule": "@typescript-eslint/no-unused-expressions"}, {"app": "gloopi-storefront", "file": "./src/components/native/CompareImage.tsx", "line": 110, "column": 9, "message": "Expected an assignment or function call and instead saw an expression.", "rule": "@typescript-eslint/no-unused-expressions"}, {"app": "gloopi-storefront", "file": "./src/components/native/CompareImage.tsx", "line": 154, "column": 13, "message": "Expected an assignment or function call and instead saw an expression.", "rule": "@typescript-eslint/no-unused-expressions"}, {"app": "gloopi-storefront", "file": "./src/components/native/CompareImage.tsx", "line": 160, "column": 17, "message": "Expected an assignment or function call and instead saw an expression.", "rule": "@typescript-eslint/no-unused-expressions"}, {"app": "gloopi-storefront", "file": "./src/components/native/CompareImage.tsx", "line": 454, "column": 1, "message": "Use \"@ts-expect-error\" instead of \"@ts-ignore\", as \"@ts-ignore\" will do nothing if the following line is error-free.", "rule": "@typescript-eslint/ban-ts-comment"}], "nextjs-optimization": [{"app": "gloopi-storefront", "file": "./src/components/native/CompareImage.tsx", "line": 410, "column": 17, "message": "Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element", "rule": "@next/next/no-img-element"}, {"app": "gloopi-storefront", "file": "./src/components/native/CompareImage.tsx", "line": 418, "column": 17, "message": "Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element", "rule": "@next/next/no-img-element"}]}, "uncategorized": []}, "fileWarnings": {"gloopi-storefront:./src/app/(store)/(routes)/blog/[slug]/page.tsx": 1, "gloopi-admin:./src/app/(store)/(routes)/blog/[slug]/page.tsx": 2, "gloopi-storefront:./src/app/(dashboard)/(routes)/banners/components/table.tsx": 4, "gloopi-admin:./src/app/(store)/(routes)/cart/components/grid.tsx": 2, "gloopi-admin:./src/app/(dashboard)/(routes)/brands/[brandId]/components/brand-form.tsx": 2, "gloopi-admin:./src/app/(dashboard)/(routes)/brands/components/table.tsx": 2, "gloopi-admin:./src/app/(dashboard)/(routes)/categories/[categoryId]/components/category-form.tsx": 2, "gloopi-admin:./src/app/(dashboard)/(routes)/categories/components/table.tsx": 2, "gloopi-admin:./src/app/(dashboard)/(routes)/orders/[orderId]/components/order-form.tsx": 1, "gloopi-admin:./src/app/(dashboard)/(routes)/orders/[orderId]/page.tsx": 2, "gloopi-admin:./src/app/(dashboard)/(routes)/payments/[paymentId]/components/payment-form.tsx": 1, "gloopi-admin:./src/app/(dashboard)/(routes)/payments/[paymentId]/page.tsx": 1, "gloopi-admin:./src/app/(dashboard)/(routes)/products/[productId]/components/product-form.tsx": 2, "gloopi-admin:./src/app/(dashboard)/(routes)/products/components/table.tsx": 1, "gloopi-admin:./src/app/(dashboard)/(routes)/users/[userId]/components/user-form.tsx": 1, "gloopi-storefront:./src/app/(dashboard)/(routes)/users/[userId]/components/user-form.tsx": 3, "gloopi-storefront:./src/app/(store)/(routes)/cart/components/item.tsx": 1, "gloopi-admin:./src/app/(dashboard)/(routes)/users/[userId]/page.tsx": 2, "gloopi-admin:./src/app/(dashboard)/(routes)/users/components/table.tsx": 1, "gloopi-storefront:./src/app/api/brands/route.ts": 7, "gloopi-admin:./src/app/(store)/(routes)/cart/components/receipt.tsx": 1, "gloopi-admin:./src/app/api/categories/route.ts": 1, "gloopi-storefront:./src/app/api/categories/route.ts": 3, "gloopi-admin:./src/app/api/products/route.ts": 2, "gloopi-storefront:./src/app/api/products/route.ts": 2, "gloopi-admin:./src/app/(store)/(routes)/products/[productId]/components/cart_button.tsx": 1, "gloopi-storefront:./src/app/login/components/user-auth-form.tsx": 3, "gloopi-storefront:./src/app/(store)/(routes)/products/[productId]/page.tsx": 2, "gloopi-storefront:./src/app/(store)/(routes)/profile/addresses/[addressId]/components/address-form.tsx": 2, "gloopi-storefront:./src/app/(store)/(routes)/profile/components/switcher.tsx": 1, "gloopi-admin:./src/app/(store)/(routes)/profile/components/switcher.tsx": 1, "gloopi-admin:./src/components/main-nav.tsx": 1, "gloopi-admin:./src/components/ui/command.tsx": 1, "gloopi-admin:./src/components/ui/data-table.tsx": 2, "gloopi-admin:./src/components/ui/input.tsx": 1, "gloopi-admin:./src/components/ui/modal.tsx": 2, "gloopi-storefront:./src/components/ui/modal.tsx": 3, "gloopi-storefront:./src/app/(store)/(routes)/profile/edit/components/user-form.tsx": 2, "gloopi-storefront:./src/app/(store)/(routes)/profile/edit/page.tsx": 3, "gloopi-admin:./src/emails/order_notification_owner.tsx": 2, "gloopi-admin:./src/emails/verify.tsx": 5, "gloopi-storefront:./src/emails/verify.tsx": 7, "gloopi-admin:./src/app/(store)/(routes)/profile/orders/[orderId]/page.tsx": 1, "gloopi-admin:./src/middleware.ts": 1, "gloopi-storefront:./src/middleware.ts": 4, "gloopi-storefront:./src/app/(store)/(routes)/wishlist/page.tsx": 2, "gloopi-storefront:./src/app/api/orders/route.ts": 2, "gloopi-storefront:./src/app/api/subscription/email/route.ts": 2, "gloopi-storefront:./src/app/api/subscription/phone/route.ts": 2, "gloopi-storefront:./src/components/native/BlogCard.tsx": 4, "gloopi-storefront:./src/components/native/CompareImage.tsx": 7, "gloopi-storefront:./src/components/native/Product.tsx": 2, "gloopi-storefront:./src/components/native/mdx/MDXComponents.tsx": 2, "gloopi-storefront:./src/components/native/modal.tsx": 2, "gloopi-storefront:./src/components/native/nav/user.tsx": 2, "gloopi-storefront:./src/components/ui/command.tsx": 1, "gloopi-storefront:./src/components/ui/input.tsx": 1, "gloopi-storefront:./src/components/ui/textarea.tsx": 1, "gloopi-storefront:./src/components/ui/use-toast.ts": 1, "gloopi-storefront:./src/emails/order_notification_owner.tsx": 2, "gloopi-storefront:./src/lib/cart.ts": 1, "gloopi-storefront:./src/lib/omit.ts": 2, "gloopi-storefront:./src/state/Cart.tsx": 2, "gloopi-storefront:./src/state/User.tsx": 1}}