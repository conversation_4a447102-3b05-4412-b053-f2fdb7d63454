{"name": "@persepolis/mail", "version": "1.0.9", "description": "Mail service for the Persepolis e-commerce platform.", "main": "dist/index.js", "scripts": {"publish": "npm publish --access public", "build": "tsc", "test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "git+https://github.com/sesto-dev/next-prisma-tailwind-ecommerce.git"}, "files": ["dist"], "author": "@sesto-dev", "license": "MIT", "bugs": {"url": "https://github.com/sesto-dev/next-prisma-tailwind-ecommerce/issues"}, "homepage": "https://github.com/sesto-dev/next-prisma-tailwind-ecommerce#readme", "devDependencies": {"@types/nodemailer": "^6.4.16", "@types/react": "^18.3.11", "typescript": "^5.6.3"}, "peerDependencies": {"@react-email/components": "^0.0.25", "@react-email/render": "^1.0.1", "nodemailer": "^6.9.4", "react": "^18.2.0"}}