{"name": "@persepolis/regex", "version": "0.0.3", "description": "Regex Package.", "main": "dist/index.js", "scripts": {"publish": "npm publish --access public", "build": "tsc", "test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "git+https://github.com/sesto-dev/next-prisma-tailwind-ecommerce.git"}, "files": ["dist"], "author": {"name": "Se<PERSON>", "url": "https://github.com/sesto-dev"}, "license": "MIT", "bugs": {"url": "https://github.com/sesto-dev/next-prisma-tailwind-ecommerce/issues"}, "homepage": "https://github.com/sesto-dev/next-prisma-tailwind-ecommerce#readme", "devDependencies": {"typescript": "^5.2.2"}, "peerDependencies": {"@types/node": "^20.6.2"}}