{"name": "@persepolis/oauth", "version": "0.0.1", "description": "OAuth service for the Persepolis e-commerce platform.", "main": "dist/index.js", "scripts": {"publish": "npm publish --access public", "build": "tsc", "test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "git+https://github.com/sesto-dev/next-prisma-tailwind-ecommerce.git"}, "files": ["dist"], "author": "@sesto-dev", "license": "MIT", "bugs": {"url": "https://github.com/sesto-dev/next-prisma-tailwind-ecommerce/issues"}, "homepage": "https://github.com/sesto-dev/next-prisma-tailwind-ecommerce#readme", "devDependencies": {"typescript": "^5.2.2"}, "peerDependencies": {"@types/node": "^20.6.2", "querystring": "^0.2.1"}}