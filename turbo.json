{"$schema": "https://turbo.build/schema.json", "ui": "tui", "tasks": {"dev": {"cache": false, "persistent": true}, "build": {"dependsOn": ["^build"], "outputs": [".next/**", "!.next/cache/**"]}, "start": {"dependsOn": ["build"], "cache": false, "persistent": true}, "lint": {"dependsOn": ["^lint"]}, "type-check": {"dependsOn": ["^type-check"]}, "db:generate": {"cache": false}, "db:format": {"cache": false}, "db:reset": {"cache": false}, "db:push": {"cache": false}, "db:seed": {"cache": false}}}